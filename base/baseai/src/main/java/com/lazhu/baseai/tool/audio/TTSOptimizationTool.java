package com.lazhu.baseai.tool.audio;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.baseai.tool.audio.dto.TTSOptimizationRequest;
import com.lazhu.baseai.tool.audio.dto.TTSOptimizationResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * TTS优化工具类
 * 用于调用TTS优化接口，将普通文本转换为带有SSML标记的优化文本
 */
@Slf4j
@Component
public class TTSOptimizationTool {

    /**
     * TTS优化接口URL
     */
    @Value("${tts.optimization.url:http://192.168.33.174/v1/workflows/run}")
    private String optimizationUrl;

    /**
     * 认证token
     */
    @Value("${tts.optimization.auth:Bearer app-zpaCirB7XVa9qmVdEY0nt814}")
    private String authToken;

    /**
     * 请求超时时间（毫秒）
     */
    @Value("${tts.optimization.timeout:30000}")
    private int timeout;

    /**
     * 优化TTS文本
     *
     * @param text 原始文本
     * @return 优化后的TTS文本
     */
    public String optimizeText(String text) {
        return optimizeText(text, "user-123");
    }

    /**
     * 优化TTS文本
     *
     * @param text 原始文本
     * @param user 用户标识
     * @return 优化后的TTS文本
     */
    public String optimizeText(String text, String user) {
        TTSOptimizationResponse response = optimizeTextWithFullResponse(text, user);
        if (response != null && response.isSuccess()) {
            return response.getOptimizedText();
        }
        log.warn("TTS优化失败，返回原始文本。错误信息: {}", 
                response != null ? response.getErrorMessage() : "响应为空");
        return text; // 如果优化失败，返回原始文本
    }

    /**
     * 优化TTS文本并返回完整响应
     *
     * @param text 原始文本
     * @param user 用户标识
     * @return 完整的响应对象
     */
    public TTSOptimizationResponse optimizeTextWithFullResponse(String text, String user) {
        if (text == null || text.trim().isEmpty()) {
            log.warn("输入文本为空，跳过TTS优化");
            return null;
        }

        try {
            // 构建请求对象
            TTSOptimizationRequest request = new TTSOptimizationRequest(text, user);
            
            // 转换为JSON
            String requestJson = JSONObject.toJSONString(request);
            log.info("TTS优化接口请求参数：{}", requestJson);

            // 创建HTTP请求
            HttpRequest httpRequest = HttpUtil.createPost(optimizationUrl)
                    .header("Authorization", authToken)
                    .header("Content-Type", "application/json")
                    .body(requestJson)
                    .timeout(timeout);

            // 发送请求
            try (HttpResponse httpResponse = httpRequest.execute()) {
                String responseBody = httpResponse.body();
                log.info("TTS优化接口响应：{}", responseBody);

                if (!httpResponse.isOk()) {
                    log.error("TTS优化接口请求失败，状态码：{}, 响应：{}", 
                            httpResponse.getStatus(), responseBody);
                    return null;
                }

                // 解析响应
                TTSOptimizationResponse response = JSONObject.parseObject(responseBody, TTSOptimizationResponse.class);
                
                if (response == null) {
                    log.error("TTS优化接口响应解析失败");
                    return null;
                }

                if (!response.isSuccess()) {
                    log.warn("TTS优化任务执行失败，状态：{}, 错误信息：{}", 
                            response.getData() != null ? response.getData().getStatus() : "unknown",
                            response.getErrorMessage());
                }

                return response;
            }

        } catch (Exception e) {
            log.error("TTS优化接口调用异常", e);
            return null;
        }
    }

    /**
     * 批量优化TTS文本
     *
     * @param texts 文本数组
     * @return 优化后的文本数组
     */
    public String[] optimizeTexts(String[] texts) {
        return optimizeTexts(texts, "user-123");
    }

    /**
     * 批量优化TTS文本
     *
     * @param texts 文本数组
     * @param user  用户标识
     * @return 优化后的文本数组
     */
    public String[] optimizeTexts(String[] texts, String user) {
        if (texts == null || texts.length == 0) {
            return new String[0];
        }

        String[] optimizedTexts = new String[texts.length];
        for (int i = 0; i < texts.length; i++) {
            optimizedTexts[i] = optimizeText(texts[i], user);
            
            // 避免请求过于频繁
            if (i < texts.length - 1) {
                try {
                    Thread.sleep(100); // 100ms间隔
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("批量优化过程中被中断");
                    break;
                }
            }
        }
        
        return optimizedTexts;
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        TTSOptimizationTool tool = new TTSOptimizationTool();
        tool.optimizationUrl = "http://192.168.33.174/v1/workflows/run";
        tool.authToken = "Bearer app-zpaCirB7XVa9qmVdEY0nt814";
        tool.timeout = 30000;
        
        String testText = "最近青岛上线生育医保异地结算，国家在拼命提升保障，但很多家庭还在为买保险吵架。";
        String optimizedText = tool.optimizeText(testText);
        
        System.out.println("原始文本：" + testText);
        System.out.println("优化后文本：" + optimizedText);
    }
}
