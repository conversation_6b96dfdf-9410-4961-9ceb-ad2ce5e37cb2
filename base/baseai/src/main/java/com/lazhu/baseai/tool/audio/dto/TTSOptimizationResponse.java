package com.lazhu.baseai.tool.audio.dto;

import lombok.Data;

/**
 * TTS优化响应DTO
 */
@Data
public class TTSOptimizationResponse {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 工作流运行ID
     */
    private String workflowRunId;
    
    /**
     * 响应数据
     */
    private ResponseData data;
    
    @Data
    public static class ResponseData {
        /**
         * ID
         */
        private String id;
        
        /**
         * 工作流ID
         */
        private String workflowId;
        
        /**
         * 状态
         */
        private String status;
        
        /**
         * 输出结果
         */
        private Outputs outputs;
        
        /**
         * 错误信息
         */
        private String error;
        
        /**
         * 执行时间（秒）
         */
        private Double elapsedTime;
        
        /**
         * 总token数
         */
        private Integer totalTokens;
        
        /**
         * 总步骤数
         */
        private Integer totalSteps;
        
        /**
         * 创建时间戳
         */
        private Long createdAt;
        
        /**
         * 完成时间戳
         */
        private Long finishedAt;
    }
    
    @Data
    public static class Outputs {
        /**
         * 优化后的TTS文本（包含SSML标记）
         */
        private String text;
    }
    
    /**
     * 获取优化后的文本
     * @return 优化后的TTS文本
     */
    public String getOptimizedText() {
        if (data != null && data.getOutputs() != null) {
            return data.getOutputs().getText();
        }
        return null;
    }
    
    /**
     * 检查任务是否成功
     * @return 是否成功
     */
    public boolean isSuccess() {
        return data != null && "succeeded".equals(data.getStatus());
    }
    
    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getErrorMessage() {
        if (data != null) {
            return data.getError();
        }
        return null;
    }
}
